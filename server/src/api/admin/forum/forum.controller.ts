import { Body, Controller, Delete, Get, Param, Patch, Post, UseGuards } from '@nestjs/common';
import { ForumService } from './forum.service';
import { Groups } from '@/api/user/decorators/groups.decorator';
import { JwtAuthGuard } from '@/api/user/guards/auth.guard';
import { AccessGuard } from '@/api/user/guards/access.guard';

@Controller('admin/forum')
@Groups('FORUM_MODERATOR')
@UseGuards(JwtAuthGuard, AccessGuard)
export class ForumController {
  constructor(private readonly forumService: ForumService) {}

  @Get('category')
  async getCategories() {
    return await this.forumService.getCategories()
  }

  @Get('category/:id')
  async getCategory(@Param('id') id: number) {
    return await this.forumService.getCategory(id)
  }

  @Post('category')
  async addCategory(@Body() body: any) {
    return await this.forumService.addCategory(body)
  }

  @Patch('category/:id')
  async updateCategory(@Param('id') id: number, @Body() body: any) {
    return await this.forumService.updateCategory(id, body)
  }

  @Delete('category/:id')
  async deleteCategory(@Param('id') id: number) {
    return await this.forumService.deleteCategory(id)
  }

  @Post('category/order')
  async updateCategoryOrder(@Body() body: any) {
    return await this.forumService.updateCategoryOrder(body);
  }
}
