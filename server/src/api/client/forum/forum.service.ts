import { Injectable, NotFoundException } from '@nestjs/common';
import {ForumCategory} from "@/entity/ForumCategory";
import {ForumTopic} from "@/entity/ForumTopic";
import {ForumTopicComment} from "@/entity/ForumTopicComment";
import {ForumTopicCommentLike} from "@/entity/ForumTopicCommentLike";
import {User} from "@/entity/User";
import {ForumTopicLike} from "@/entity/ForumTopicLike";
import {ForumTopicFavorite} from "@/entity/ForumTopicFavorite";
import {ForumTopicCommentFavorite} from "@/entity/ForumTopicCommentFavorite";

@Injectable()
export class ForumService {
  async getCategory(id: number, user: any, page: number, limit: number = 5, sort = 'dateDesc') {
    const category = await ForumCategory.findOne({
      where: { id },
    });

    if (!category) {
      throw new NotFoundException('Категория не найдена');
    }

    let topics: ForumTopic[];
    let totalTopics: number;

    const relations = [
      'user',
      'images',
      'category',
      'comments',
      'comments.user',
      'likes',
      'favorites'
    ];

    let orderOptions = {};
    switch (sort) {
      case 'dateAsc':
        orderOptions = { createdAt: 'ASC' };
        break;
      case 'viewsAsc':
        orderOptions = { views: 'ASC' };
        break;
      case 'viewsDesc':
        orderOptions = { views: 'DESC' };
        break;
      case 'lastReplyAsc':
        orderOptions = { lastReplyAt: 'ASC' };
        break;
      case 'lastReplyDesc':
        orderOptions = { lastReplyAt: 'DESC' };
        break;
      case 'dateDesc':
      default:
        orderOptions = { createdAt: 'DESC' };
        break;
    }

    [topics, totalTopics] = await ForumTopic.findAndCount({
      where: { category: { id: id } },
      relations: relations,
      order: orderOptions,
      skip: (page - 1) * limit,
      take: limit,
    });

    for (let topic of topics) {
      topic['access'] = this.getAccess(user, topic);
    }

    return {
      ...category,
      topics: topics,
      totalTopics: totalTopics,
      currentPage: page,
      totalPages: Math.ceil(totalTopics / limit)
    };
  }

    getAccess(user: any, topic: any) {
        if(!user) return [];
        if(user.id === topic.user.id ||
            ['ADMIN', 'FORUM_ADMIN', 'FORUM_MODERATOR'].some(e => user?.groups?.includes(e))
        ) {
          return ['edit', 'remove']
        }
        return []
    }

    async getCategories(user: any) {
        const categories = await ForumCategory.find({
            order: {
                order: 'ASC'
            },
            relations: ['topics.comments'],
            where: {
              active: true
            }
        });
        return categories.filter(category => {
            return !category.unavailableStatuses.some(status => user?.statuses?.includes(status));
        });
    }

    async getTopic(id: number, user: any) {
        const topic = await ForumTopic.findOne({
            where: {id},
            relations: ['images', 'category', 'user', 'user.avatar', 'comments', 'comments.user', 'comments.user.avatar', 'comments.likes', 'comments.likes.user', 'likes', 'likes.user', 'favorites', 'favorites.user', 'comments.favorites', 'comments.favorites.user', 'comments.reply.user', 'comments.reply.user.avatar'],
            order: {
                comments: {
                    createdAt: 'ASC'
                }
            },
          select: {
              comments: {
                id: true,
                createdAt: true,
                comment: true,
                likes: true,
                favorites: true,
                user: true
              }
          }
        })
        if(user && topic.comments) {
            for(let i in topic.comments) {
                if(topic.comments[i].likes.some(l => l.user.id === user.id)) {
                    topic.comments[i]['isLiked'] = true
                }
                if(topic.comments[i].favorites.some(l => l.user.id === user.id)) {
                    topic.comments[i]['isFavorite'] = true
                }
                if(topic.comments[i].user.id === user.id || ['ADMIN', 'FORUM_MODERATOR'].some(e => user.groups?.includes(e))) {
                    topic.comments[i]['access'] = ['edit', 'remove']
                }
            }
            if(topic.likes.some(l => l.user.id === user.id)) {
                topic['isLiked'] = true
            }
            if(topic.favorites.some(l => l.user.id === user.id)) {
                topic['isFavorite'] = true
            }
        }
        await ForumTopic.update(id, {views: ++topic.views})
        return topic
    }

    async createTopic(body: any, user: any) {
        if(body.id) {
            const topic = await ForumTopic.findOne({
                where: {id: body.id},
                relations: ['images']
            })
            topic.images = body.images;
            await topic.save();
            delete body.images;
            delete body.id;
            return await ForumTopic.update(topic.id, body)
        }
        return await ForumTopic.save({...body, user: user.id})
    }

    async deleteTopic(id: number) {
        return await ForumTopic.delete(id)
    }

    async saveComment(body: any, user: any) {
        let reply = null

        ForumTopic.update(body.topic, { lastReplyAt: new Date()});

        if(body.id) {
            return await ForumTopicComment.update(body.id, {comment: body.comment})
        }
        // const comment = await ForumTopicComment.findOne({
        //     where: {
        //         reply: {
        //             id: body.reply.id
        //         }
        //     }
        // })
        if(body.reply) {
            reply = [{id: body.reply.id}]
        }
        return await ForumTopicComment.save({...body, user: user.id, reply})
    }

    async likeComment(id: number, user: any) {
        const liked = await ForumTopicCommentLike.findOneBy({topicComment: {id}, user: {id: user.id}})
        if(liked) {
            return await ForumTopicCommentLike.delete(liked.id)
        }
        const topic = await ForumTopicComment.findOne({
            where: {id},
            relations: ['likes']
        });
        const like = await ForumTopicCommentLike.save({
            user,
            topicComment: {id}
        })
        topic.likes = [...topic.likes, like];
        return await topic.save();
    }

    async favoriteComment(id: number, user: any) {
        const liked = await ForumTopicCommentFavorite.findOneBy({topicComment: {id}, user: {id: user.id}})
        if(liked) {
            return await ForumTopicCommentFavorite.delete(liked.id)
        }
        const topic = await ForumTopicComment.findOne({
            where: {id},
            relations: ['favorites']
        });
        const like = await ForumTopicCommentFavorite.save({
            user,
            topicComment: {id}
        })
        topic.favorites = [...topic.favorites, like];
        return await topic.save();
    }

    async likeTopic(id: number, user: any) {
        const liked = await ForumTopicLike.findOneBy({topic: {id}, user: {id: user.id}})
        if(liked) {
            return await ForumTopicLike.delete(liked.id)
        }
        const topic = await ForumTopic.findOne({
            where: {id},
            relations: ['likes']
        });
        const like = await ForumTopicLike.save({
            user,
            topic: {id}
        })
        topic.likes = [...topic.likes, like];
        return await topic.save();
    }

    async favoriteTopic(id: number, user: any) {
        const isFavorite = await ForumTopicFavorite.findOneBy({topic: {id}, user: {id: user.id}})
        if(isFavorite) {
            return await ForumTopicFavorite.delete(isFavorite.id)
        }
        const topic = await ForumTopic.findOne({
            where: {id},
            relations: ['favorites']
        });
        const like = await ForumTopicFavorite.save({
            user,
            topic: {id}
        })
        topic.favorites = [...topic.favorites, like];
        return await topic.save();
    }

    async deleteComment(id: number) {
        return await ForumTopicComment.delete(id)
    }

    async getFavourites(userId: number) {
        const userEntity: any = await User.findOne({
            where: {id: userId},
            relations: [
                'topicFavorites.topic.category',
                'topicFavorites.topic.likes.user',
                'topicCommentFavorites.topicComment.topic.category',
                'topicFavorites.topic.comments',
                'topicFavorites.topic.comments.user',
                'topicCommentFavorites.topicComment.likes.user'
            ]
        });

        for(let i in userEntity.topicFavorites) {
            userEntity.topicFavorites[i] = {
                ...userEntity.topicFavorites[i],
                topic: {
                    ...userEntity.topicFavorites[i].topic,
                    likes: userEntity.topicFavorites[i].topic.likes.length,
                    liked: userEntity.topicFavorites[i].topic.likes.some((e) => e.user.id === userId)
                }
            }
        }

        for(let i in userEntity.topicCommentFavorites) {
            userEntity.topicCommentFavorites[i] = {
                ...userEntity.topicCommentFavorites[i],
                topicComment: {
                    ...userEntity.topicCommentFavorites[i].topicComment,
                    likes: userEntity.topicCommentFavorites[i].topicComment.likes.length,
                    liked: userEntity.topicCommentFavorites[i].topicComment.likes.some((e) => e.user.id === userId)
                }
            }
        }

        return userEntity;
    }
}
