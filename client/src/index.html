<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <title></title>
  <meta name="description" content="">
  <base href="/">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="icon" type="image/x-icon" href="favicon.ico">
</head>
<body>
  <style>
    /* Prevent content jumping during load */
    body {
      overflow: hidden;
    }

    body.loaded {
      overflow: auto;
    }

    app-root {
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    app-root.loaded {
      opacity: 1;
    }

    .preloader {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg,
        var(--light-color, rgba(249, 233, 200, 0.95)) 0%,
        var(--selection, rgba(255, 226, 163, 0.95)) 50%,
        var(--pl-stop, rgba(240, 187, 86, 0.95)) 100%);
      backdrop-filter: blur(8px);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 1000;
      transition: opacity 0.5s ease, visibility 0.5s ease;
      opacity: 1;
    }

    .yoga-spinner {
      position: relative;
      width: 120px;
      height: 120px;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .om-symbol {
      position: absolute;
      font-size: 52px;
      font-weight: 300;
      color: var(--text-color, #dea53d);
      z-index: 3;
      animation: gentleFloat 4s ease-in-out infinite;
      user-select: none;
      opacity: 0.95;
    }

    .energy-wave {
      position: absolute;
      border: 2px solid var(--text-color, rgba(222, 165, 61, 0.3));
      border-radius: 50%;
      animation: waveExpand 3s ease-out infinite;
    }

    .energy-wave:nth-child(1) {
      width: 60px;
      height: 60px;
      animation-delay: 0s;
    }

    .energy-wave:nth-child(2) {
      width: 80px;
      height: 80px;
      animation-delay: 1s;
    }

    .energy-wave:nth-child(3) {
      width: 100px;
      height: 100px;
      animation-delay: 2s;
    }



    @keyframes gentleFloat {
      0%, 100% {
        transform: translateY(0px) scale(1);
      }
      50% {
        transform: translateY(-8px) scale(1.02);
      }
    }

    @keyframes waveExpand {
      0% {
        transform: scale(0.8);
        opacity: 0.8;
      }
      50% {
        opacity: 0.4;
      }
      100% {
        transform: scale(1.5);
        opacity: 0;
      }
    }



    .preloader-hidden {
      opacity: 0;
      pointer-events: none;
      visibility: hidden;
    }


  </style>
  <div class="preloader" id="preloader">
    <div class="yoga-spinner">
      <div class="meditation-dots">
        <div class="meditation-dot"></div>
        <div class="meditation-dot"></div>
        <div class="meditation-dot"></div>
        <div class="meditation-dot"></div>
        <div class="meditation-dot"></div>
        <div class="meditation-dot"></div>
      </div>
      <div class="lotus-ring"></div>
      <div class="inner-ring"></div>
      <div class="om-symbol">ॐ</div>
    </div>
  </div>
  <app-root></app-root>
</body>
</html>
