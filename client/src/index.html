<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <title></title>
  <meta name="description" content="">
  <base href="/">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="icon" type="image/x-icon" href="favicon.ico">
</head>
<body>
  <style>
    /* Prevent content jumping during load */
    body {
      overflow: hidden;
    }

    body.loaded {
      overflow: auto;
    }

    app-root {
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    app-root.loaded {
      opacity: 1;
    }

    .preloader {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg,
        var(--light-color, rgba(249, 233, 200, 0.95)) 0%,
        var(--selection, rgba(255, 226, 163, 0.95)) 50%,
        var(--pl-stop, rgba(240, 187, 86, 0.95)) 100%);
      backdrop-filter: blur(8px);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 1000;
      transition: opacity 0.5s ease, visibility 0.5s ease;
      opacity: 1;
    }

    .yoga-spinner {
      position: relative;
      width: 200px;
      height: 200px;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .namaste-symbol {
      position: absolute;
      width: 80px;
      height: 80px;
      background-image: url('assets/images/icons/namaste_.svg');
      background-size: 400% 200%;
      background-repeat: no-repeat;
      background-position: center;
      z-index: 3;
      animation: namasteGlow 3s ease-in-out infinite;
      user-select: none;
    }

    .peaceful-circle {
      position: absolute;
      border-radius: 50%;
      border: 1px solid var(--text-color, rgba(222, 165, 61, 0.3));
      animation: peacefulPulse 4s ease-in-out infinite;
    }

    .circle-1 {
      width: 120px;
      height: 120px;
      animation-delay: 0s;
    }

    .circle-2 {
      width: 150px;
      height: 150px;
      animation-delay: 1s;
    }

    .circle-3 {
      width: 180px;
      height: 180px;
      animation-delay: 2s;
    }



    @keyframes namasteGlow {
      0%, 100% {
        opacity: 0.8;
        filter: brightness(1);
      }
      50% {
        opacity: 1;
        filter: brightness(1.2);
      }
    }

    @keyframes peacefulPulse {
      0%, 100% {
        transform: scale(1);
        opacity: 0.3;
      }
      50% {
        transform: scale(1.1);
        opacity: 0.6;
      }
    }



    .preloader-hidden {
      opacity: 0;
      pointer-events: none;
      visibility: hidden;
    }


  </style>
  <div class="preloader" id="preloader">
    <div class="yoga-spinner">
      <div class="peaceful-circle circle-3"></div>
      <div class="peaceful-circle circle-2"></div>
      <div class="peaceful-circle circle-1"></div>
      <div class="namaste-symbol"></div>
    </div>
  </div>
  <app-root></app-root>
</body>
</html>
