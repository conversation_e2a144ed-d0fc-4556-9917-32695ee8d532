<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <title></title>
  <meta name="description" content="">
  <base href="/">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="icon" type="image/x-icon" href="favicon.ico">
</head>
<body>
  <style>
    /* Prevent content jumping during load */
    body {
      overflow: hidden;
    }

    body.loaded {
      overflow: auto;
    }

    app-root {
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    app-root.loaded {
      opacity: 1;
    }

    .preloader {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg,
        var(--light-color, rgba(249, 233, 200, 0.95)) 0%,
        var(--selection, rgba(255, 226, 163, 0.95)) 50%,
        var(--pl-stop, rgba(240, 187, 86, 0.95)) 100%);
      backdrop-filter: blur(8px);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 1000;
      transition: opacity 0.5s ease, visibility 0.5s ease;
      opacity: 1;
    }

    .yoga-spinner {
      position: relative;
      width: 120px;
      height: 120px;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .om-symbol {
      position: absolute;
      font-size: 40px;
      font-weight: normal;
      color: var(--text-color, #dea53d);
      z-index: 3;
      user-select: none;
    }

    .spinner-ring {
      position: absolute;
      border-radius: 50%;
      border: 2px solid transparent;
    }

    .ring-1 {
      width: 60px;
      height: 60px;
      border-top: 2px solid var(--text-color, rgba(222, 165, 61, 0.8));
      border-right: 2px solid var(--text-color, rgba(222, 165, 61, 0.4));
      animation: spin 2s linear infinite;
    }

    .ring-2 {
      width: 80px;
      height: 80px;
      border-bottom: 2px solid var(--text-color, rgba(222, 165, 61, 0.6));
      border-left: 2px solid var(--text-color, rgba(222, 165, 61, 0.3));
      animation: spin 3s linear infinite reverse;
    }

    .ring-3 {
      width: 100px;
      height: 100px;
      border-top: 2px solid var(--text-color, rgba(222, 165, 61, 0.4));
      border-right: 2px solid var(--text-color, rgba(222, 165, 61, 0.2));
      animation: spin 4s linear infinite;
    }



    @keyframes omRotate {
      0% {
        transform: rotate(0deg);
      }
      100% {
        transform: rotate(360deg);
      }
    }

    @keyframes petalBreathe {
      0%, 100% {
        transform: scale(1) rotate(var(--rotation, 0deg)) translateY(-40px);
        opacity: 0.6;
      }
      50% {
        transform: scale(1.1) rotate(var(--rotation, 0deg)) translateY(-45px);
        opacity: 0.9;
      }
    }



    .preloader-hidden {
      opacity: 0;
      pointer-events: none;
      visibility: hidden;
    }


  </style>
  <div class="preloader" id="preloader">
    <div class="yoga-spinner">
      <div class="lotus-petal"></div>
      <div class="lotus-petal"></div>
      <div class="lotus-petal"></div>
      <div class="lotus-petal"></div>
      <div class="lotus-petal"></div>
      <div class="lotus-petal"></div>
      <div class="lotus-petal"></div>
      <div class="lotus-petal"></div>
      <div class="om-symbol">ॐ</div>
    </div>
  </div>
  <app-root></app-root>
</body>
</html>
