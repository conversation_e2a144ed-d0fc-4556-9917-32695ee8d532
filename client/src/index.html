<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <title></title>
  <meta name="description" content="">
  <base href="/">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="icon" type="image/x-icon" href="favicon.ico">
</head>
<body>
  <style>
    .preloader {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg,
        var(--light-color, rgba(249, 233, 200, 0.95)) 0%,
        var(--selection, rgba(255, 226, 163, 0.95)) 50%,
        var(--pl-stop, rgba(240, 187, 86, 0.95)) 100%);
      backdrop-filter: blur(8px);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 1000;
      transition: opacity 0.5s ease, visibility 0.5s ease;
      opacity: 1;
    }

    .yoga-spinner {
      position: relative;
      width: 120px;
      height: 120px;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .om-symbol {
      position: absolute;
      width: 60px;
      height: 60px;
      background-image: url('assets/images/icons/om_main.svg');
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center;
      z-index: 2;
      animation: omPulse 2s ease-in-out infinite;
    }

    .lotus-ring {
      position: absolute;
      width: 100px;
      height: 100px;
      border: 3px solid transparent;
      border-radius: 50%;
      background: linear-gradient(45deg,
        var(--text-color, rgba(222, 165, 61, 0.8)),
        var(--pl-stop, rgba(240, 187, 86, 0.8)),
        var(--p-line, rgba(154, 97, 25, 0.8))) border-box;
      -webkit-mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
      -webkit-mask-composite: subtract;
      mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
      mask-composite: subtract;
      animation: lotusRotate 3s linear infinite;
    }

    .inner-ring {
      position: absolute;
      width: 80px;
      height: 80px;
      border: 2px solid var(--p-line, rgba(154, 97, 25, 0.4));
      border-radius: 50%;
      border-top: 2px solid var(--p-line, rgba(154, 97, 25, 0.8));
      animation: innerRotate 1.5s linear infinite reverse;
    }

    .meditation-dots {
      position: absolute;
      width: 140px;
      height: 140px;
    }

    .meditation-dot {
      position: absolute;
      width: 8px;
      height: 8px;
      background: radial-gradient(circle,
        var(--text-color, rgba(222, 165, 61, 0.9)) 0%,
        var(--p-line, rgba(154, 97, 25, 0.7)) 100%);
      border-radius: 50%;
      animation: dotFloat 4s ease-in-out infinite;
    }

    .meditation-dot:nth-child(1) { top: 0; left: 50%; transform: translateX(-50%); animation-delay: 0s; }
    .meditation-dot:nth-child(2) { top: 25%; right: 0; animation-delay: 0.5s; }
    .meditation-dot:nth-child(3) { bottom: 25%; right: 0; animation-delay: 1s; }
    .meditation-dot:nth-child(4) { bottom: 0; left: 50%; transform: translateX(-50%); animation-delay: 1.5s; }
    .meditation-dot:nth-child(5) { bottom: 25%; left: 0; animation-delay: 2s; }
    .meditation-dot:nth-child(6) { top: 25%; left: 0; animation-delay: 2.5s; }

    .loading-text {
      position: absolute;
      bottom: -60px;
      left: 50%;
      transform: translateX(-50%);
      font-family: 'Prata', serif;
      font-size: 16px;
      color: var(--font-color, rgba(83, 46, 0, 0.8));
      letter-spacing: 2px;
      animation: textGlow 2s ease-in-out infinite alternate;
    }

    @keyframes omPulse {
      0%, 100% {
        transform: scale(1);
        filter: drop-shadow(0 0 10px var(--text-color, rgba(222, 165, 61, 0.3)));
      }
      50% {
        transform: scale(1.1);
        filter: drop-shadow(0 0 20px var(--text-color, rgba(222, 165, 61, 0.6)));
      }
    }

    @keyframes lotusRotate {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    @keyframes innerRotate {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    @keyframes dotFloat {
      0%, 100% {
        opacity: 0.3;
        transform: scale(0.8);
      }
      50% {
        opacity: 1;
        transform: scale(1.2);
      }
    }

    @keyframes textGlow {
      0% {
        opacity: 0.7;
        text-shadow: 0 0 5px var(--text-color, rgba(222, 165, 61, 0.3));
      }
      100% {
        opacity: 1;
        text-shadow: 0 0 15px var(--text-color, rgba(222, 165, 61, 0.6));
      }
    }

    .preloader-hidden {
      opacity: 0;
      pointer-events: none;
      visibility: hidden;
    }

    /* Dark theme support */
    @media (prefers-color-scheme: dark) {
      .preloader {
        background: linear-gradient(135deg, rgba(48, 52, 57, 0.95) 0%, rgba(17, 17, 17, 0.95) 50%, rgba(51, 51, 51, 0.95) 100%);
      }

      .loading-text {
        color: rgba(222, 165, 61, 0.9);
      }
    }
  </style>
  <div class="preloader" id="preloader">
    <div class="yoga-spinner">
      <div class="meditation-dots">
        <div class="meditation-dot"></div>
        <div class="meditation-dot"></div>
        <div class="meditation-dot"></div>
        <div class="meditation-dot"></div>
        <div class="meditation-dot"></div>
        <div class="meditation-dot"></div>
      </div>
      <div class="lotus-ring"></div>
      <div class="inner-ring"></div>
      <div class="om-symbol"></div>
      <div class="loading-text">ॐ</div>
    </div>
  </div>
  <app-root></app-root>
</body>
</html>
