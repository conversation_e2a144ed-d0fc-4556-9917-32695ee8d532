import { Injectable, Inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';

@Injectable({
  providedIn: 'root'
})
export class SpinnerService {
  private preloader: HTMLElement | null = null;
  private isBrowser: boolean;

  constructor(
    @Inject(PLATFORM_ID) private platformId: Object
  ) {
    this.isBrowser = isPlatformBrowser(this.platformId);
    
    // Выполняем инициализацию только в браузере
    if (this.isBrowser) {
      // Отложим инициализацию, чтобы DOM успел загрузиться
      setTimeout(() => {
        this.initializeSpinner();
      });
    }
  }

  private initializeSpinner(): void {
    // Убедимся, что мы в браузере и DOM доступен
    if (!this.isBrowser) return;

    this.preloader = document.getElementById('preloader');

    // Скрываем спиннер после первой загрузки приложения
    if (this.preloader) {
      // Устанавливаем таймер для скрытия прелоадера только при инициализации
      setTimeout(() => {
        this.hideSpinner();
      }, 300); // Небольшая задержка для завершения анимаций
    }

    // Удалено: подписка на события роутера для навигации
    // В SPA навигация должна быть мгновенной без показа глобального лоадера
  }

  showSpinner(): void {
    if (!this.isBrowser || !this.preloader) return;

    // Блокируем прокрутку body
    document.body.classList.remove('loaded');

    // Скрываем app-root
    const appRoot = document.querySelector('app-root');
    if (appRoot) {
      appRoot.classList.remove('loaded');
    }

    // Показываем прелоадер
    this.preloader.classList.remove('preloader-hidden');
    // Трюк для принудительной перерисовки в iOS
    void this.preloader.offsetWidth;
  }

  hideSpinner(): void {
    if (!this.isBrowser || !this.preloader) return;

    // Добавляем класс loaded к app-root для плавного появления
    const appRoot = document.querySelector('app-root');
    if (appRoot) {
      appRoot.classList.add('loaded');
    }

    // Разрешаем прокрутку body
    document.body.classList.add('loaded');

    // Скрываем прелоадер
    this.preloader.classList.add('preloader-hidden');
  }
}
