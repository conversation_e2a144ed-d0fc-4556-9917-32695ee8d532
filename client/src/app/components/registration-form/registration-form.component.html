<div class="registration-page flex flex-col gap-[30px] justify-center items-center">
  <div class="registration-title">Намасте!</div>
  <img src="assets/images/login-icon.svg" alt="registration-icon" width="336" height="38">
  <form class="flex flex-col gap-[20px]" [formGroup]="registrationForm" (submit)="registrationFormSubmit()">
    <div class="form-control">
      <label for="email">E-mail</label>
      <input type="text" formControlName="email">
    </div>
    <div class="form-control">
      <label for="password">Пароль</label>
      <div class="input-wrap">
        <input id="password" [type]="showPasswordState ? 'text' : 'password'" formControlName="password">
        <span class="password-suffix" (click)="showPassword()"></span>
      </div>
    </div>
    <div class="form-control">
      <label for="confirmPassword">Повторите пароль</label>
      <div class="input-wrap">
        <input id="confirmPassword" [type]="showConfirmPasswordState ? 'text' : 'password'" formControlName="confirmPassword">
        <span class="password-suffix" (click)="showPassInConfirm()"></span>
      </div>
    </div>
    <div style="color: red;" *ngIf="registrationForm.value.password != registrationForm.value.confirmPassword">Пароли не совпадают</div>
    <div sc-checkbox-re-captcha siteKey="6LdHg4IrAAAAAP713d1wv8qRL0xZuz8HeRSscY4P" formControlName="captcha"></div>
    <!-- @if(errors) {
    <div style="color: red">
      {{errors | json}}
    </div>
    }
    @if(success) {
    <div style="color: green">
      Вы успешно зарегистрировались!<br>
      <a [routerLink]="['/ru/signin']">Войти</a>
    </div>
    } -->

    <button class="button_sign_in" type="submit" [disabled]="registrationForm.invalid">Зарегистрироваться</button>
    <button type="button" class="button_sign_in_google flex items-center justify-center gap-[10px]"  (click)="signInGoogle()">
      <img src="../../../assets/images/icons/google-icon.svg" width="26" height="26" alt="google">
      <span>Войти с помощью Google</span>
    </button>
    <div class="already-have-an-account">Уже есть аккаунт? <a routerLink="/ru/signin">Войти</a></div>
  </form>
</div>
